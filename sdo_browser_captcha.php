<?php
/**
 * SDO浏览器验证码页面
 */

// 包含系统文件
include("./includes/common.php");

// 获取参数
$trade_no = $_GET['trade_no'] ?? '';
$orderToken = $_GET['orderToken'] ?? '';
$orderId = $_GET['orderId'] ?? '';
$payToken = $_GET['payToken'] ?? '';
$payChannelId = $_GET['payChannelId'] ?? '';
$captchaInfo = $_GET['captchaInfo'] ?? '';

// 验证必要参数
if (empty($trade_no) || empty($orderToken) || empty($orderId) || empty($payToken)) {
    exit('缺少必要参数');
}

// 解码验证码信息
$captchaData = [];
if (!empty($captchaInfo)) {
    $captchaData = json_decode(base64_decode($captchaInfo), true);
}

// 获取订单信息
$order = $DB->getRow("SELECT * FROM pre_order WHERE trade_no=? LIMIT 1", [$trade_no]);
if (!$order) {
    exit('订单不存在');
}

// 处理验证码提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    ob_clean();
    header('Content-Type: application/json');
    
    try {
        $geetest_challenge = $_POST['geetest_challenge'] ?? '';
        $geetest_validate = $_POST['geetest_validate'] ?? '';
        $geetest_seccode = $_POST['geetest_seccode'] ?? '';
        
        if (empty($geetest_challenge) || empty($geetest_validate) || empty($geetest_seccode)) {
            throw new Exception('验证码参数不完整');
        }
        
        // 获取通道信息
        $channel = $DB->getRow("SELECT * FROM pre_channel WHERE id=? LIMIT 1", [$order['channel']]);
        if (!$channel) {
            throw new Exception('支付通道不存在');
        }
        
        $cookie = $channel['appurl'];
        
        // 构造最终支付请求
        $postData = [
            'orderId' => $orderId,
            'orderToken' => $orderToken,
            'parentChannelId' => '3',
            'payChannelId' => intval($payChannelId),
            'payToken' => $payToken,
            'captchaInfo' => json_encode([
                'picCode' => 'gtest',
                'gtData' => [
                    'challenge' => $geetest_challenge,
                    'validate' => $geetest_validate,
                    'seccode' => $geetest_seccode
                ]
            ])
        ];
        
        // 发起最终支付请求
        require_once('plugins/sdopay/sdopay_plugin.php');
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://pay.sdo.com/cashier/go');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
        curl_setopt($ch, CURLOPT_COOKIE, $cookie);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        $data = json_decode($response, true);
        
        if ($data && $data['return_code'] === 0) {
            $paymentUrl = '';
            if (isset($data['data']['nextStepContent']['url'])) {
                $paymentUrl = $data['data']['nextStepContent']['url'];
                if (!empty($data['data']['nextStepContent']['queryString'])) {
                    $paymentUrl .= '?' . $data['data']['nextStepContent']['queryString'];
                }
            } elseif (isset($data['data']['url'])) {
                $paymentUrl = $data['data']['url'];
            }
            
            if (!empty($paymentUrl)) {
                echo json_encode([
                    'success' => true,
                    'message' => '验证码验证成功',
                    'url' => $paymentUrl
                ], JSON_UNESCAPED_UNICODE);
                exit;
            }
        }
        
        echo json_encode([
            'success' => false,
            'message' => '验证码验证失败: ' . ($data['return_message'] ?? '未知错误'),
            'response' => $response
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
    exit;
}

// 重新获取新的验证码参数（因为challenge会过期）
$challenge = '';
$gt = '';

// 获取通道信息
$channel = $DB->getRow("SELECT * FROM pre_channel WHERE id=? LIMIT 1", [$order['channel']]);
if ($channel) {
    $cookie = $channel['appurl'];

    // 重新访问支付页面获取新的验证码参数
    $payUrl = "https://pay.sdo.com/cashier/pay?orderToken={$orderToken}&orderId={$orderId}";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $payUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_COOKIE, $cookie);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    $response = curl_exec($ch);
    curl_close($ch);

    // 从响应中提取新的验证码参数
    if ($response && preg_match('/window\.initData\s*=\s*({[^}]+})/', $response, $matches)) {
        $initData = json_decode($matches[1], true);
        if (isset($initData['captchaInfo'])) {
            $newCaptchaInfo = json_decode($initData['captchaInfo'], true);
            if (isset($newCaptchaInfo['captchaParams']['gtData'])) {
                $challenge = $newCaptchaInfo['captchaParams']['gtData']['challenge'] ?? '';
                $gt = $newCaptchaInfo['captchaParams']['gtData']['gt'] ?? '';
            }
        }
    }

    // 如果没有获取到新参数，尝试从原始参数中获取（备用方案）
    if (empty($challenge) && isset($captchaData['captchaParams']['gtData'])) {
        $challenge = $captchaData['captchaParams']['gtData']['challenge'] ?? '';
        $gt = $captchaData['captchaParams']['gtData']['gt'] ?? '';
    }
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SDO支付验证码</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 40px;
            max-width: 500px;
            width: 100%;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .captcha-container {
            text-align: center;
            margin: 30px 0;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin: 10px 0;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
    <script src="https://static.geetest.com/static/tools/gt.js"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>🔐 SDO支付验证码</h2>
            <p>请完成验证码验证以继续支付</p>
        </div>
        
        <div class="info">
            <p><strong>订单号：</strong><?php echo htmlspecialchars($trade_no); ?></p>
            <p><strong>支付金额：</strong><?php echo htmlspecialchars($order['money']); ?>元</p>
            <p><strong>支付方式：</strong>支付宝</p>
        </div>
        
        <div class="captcha-container">
            <div id="captcha-box">
                <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                    <p>🔄 正在加载验证码...</p>
                    <p style="font-size: 12px; color: #666;">请稍候，验证码正在初始化</p>
                </div>
            </div>
        </div>

        <div style="margin: 20px 0;">
            <button type="button" class="btn" onclick="refreshCaptcha()" id="refreshBtn">
                🔄 刷新验证码
            </button>

            <button type="button" class="btn" onclick="jumpToOfficial()" style="background: #007bff; margin-top: 10px;">
                🏢 跳转到官方验证页面
            </button>

            <button type="button" class="btn" onclick="tryAutoBypass()" id="autoBypassBtn" style="background: #28a745; margin-top: 10px;">
                🚀 尝试自动绕过验证码
            </button>

            <button type="button" class="btn" onclick="showManualForm()" style="background: #6c757d; margin-top: 10px;">
                ✋ 手动输入验证码结果
            </button>
        </div>

        <form id="captchaForm" style="display: none;">
            <div class="info" style="background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460;">
                <p><strong>手动验证码模式</strong></p>
                <p>请在新窗口中完成SDO验证码，然后将结果填入下方：</p>
            </div>

            <div style="margin: 15px 0;">
                <label>Challenge:</label>
                <input type="text" id="manual_challenge" class="form-control" value="<?php echo $challenge; ?>" readonly style="width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px;">
            </div>

            <div style="margin: 15px 0;">
                <label>Validate (验证结果):</label>
                <input type="text" id="manual_validate" class="form-control" placeholder="请输入验证码validate值" style="width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px;">
            </div>

            <button type="submit" class="btn" id="submitBtn">
                提交验证码结果
            </button>

            <button type="button" class="btn" onclick="hideManualForm()" style="background: #6c757d; margin-top: 10px;">
                取消
            </button>
        </form>
        
        <div id="result" class="result"></div>
    </div>

    <script>
        var captcha = null;
        var gt = '<?php echo $gt; ?>';
        var challenge = '<?php echo $challenge; ?>';
        var captchaInfo = <?php
            // 如果captchaInfo是base64编码的字符串，先解码
            if (is_string($captchaInfo) && !is_array($captchaInfo)) {
                $decodedInfo = base64_decode($captchaInfo);
                $parsedInfo = json_decode($decodedInfo, true);
                echo json_encode($parsedInfo);
            } else {
                echo json_encode($captchaInfo);
            }
        ?>;

        // 初始化验证码
        function initCaptcha() {
            // 使用固定的gt值
            gt = '31cc9ac8ae5eb9ef1aeaee9110bfc50c';

            // 优先使用从captchaInfo中解析出来的challenge
            if (captchaInfo && captchaInfo.captchaParams && captchaInfo.captchaParams.gtData && captchaInfo.captchaParams.gtData.challenge) {
                challenge = captchaInfo.captchaParams.gtData.challenge;
                console.log('使用captchaInfo中的challenge:', challenge);
                initGeetestCaptcha();
                return;
            }

            // 如果没有captchaInfo，则从极验API获取新的challenge
            console.log('正在获取新的验证码参数...');

            fetch('https://api.geetest.com/register.php?gt=' + gt + '&client_type=web&ip_address=')
            .then(response => response.text())
            .then(data => {
                console.log('获取到验证码数据:', data);
                try {
                    var result = JSON.parse(data);
                    if (result.challenge) {
                        challenge = result.challenge;
                        console.log('使用JSON格式的challenge:', challenge);
                        initGeetestCaptcha();
                    } else {
                        throw new Error('JSON中没有challenge字段');
                    }
                } catch (e) {
                    // 如果返回的不是JSON，可能是challenge字符串
                    if (data.length === 32) {
                        challenge = data;
                        console.log('使用字符串格式的challenge:', challenge);
                        initGeetestCaptcha();
                    } else {
                        console.error('验证码参数格式错误:', data);
                        // 使用备用challenge
                        challenge = generateBackupChallenge();
                        console.log('使用备用challenge:', challenge);
                        initGeetestCaptcha();
                    }
                }
            })
            .catch(error => {
                console.error('获取验证码参数失败:', error);
                // 使用备用方案
                challenge = generateBackupChallenge();
                console.log('网络错误，使用备用challenge:', challenge);
                initGeetestCaptcha();
            });
        }

        // 生成备用challenge
        function generateBackupChallenge() {
            var chars = '0123456789abcdef';
            var result = '';
            for (var i = 0; i < 32; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        }

        function initGeetestCaptcha() {
            console.log('初始化验证码，gt:', gt, 'challenge:', challenge);

            // 检查极验库是否加载
            if (typeof initGeetest === 'undefined') {
                console.error('极验验证码库未加载');
                showError('验证码库未加载，请刷新页面重试');
                return;
            }

            // 清空验证码容器
            document.getElementById('captcha-box').innerHTML = '<div style="text-align: center; padding: 20px;">🔄 正在初始化验证码...</div>';

            // 初始化验证码
            initGeetest({
                gt: gt,
                challenge: challenge,
                offline: false,
                new_captcha: true,
                product: 'bind',
                width: '100%',
                https: true,
                lang: 'zh-cn'
            }, function (captchaObj) {
                console.log('验证码对象创建成功');
                window.captchaObj = captchaObj;

                // 清空容器并添加验证码
                document.getElementById('captcha-box').innerHTML = '';
                captchaObj.appendTo('#captcha-box');

                captchaObj.onReady(function() {
                    console.log('验证码加载完成，可以开始验证');
                });

                captchaObj.onSuccess(function() {
                    var result = captchaObj.getValidate();
                    console.log('验证成功，结果:', result);

                    if (result && result.geetest_challenge && result.geetest_validate && result.geetest_seccode) {
                        // 自动提交验证结果
                        submitCaptchaResult(result.geetest_challenge, result.geetest_validate, result.geetest_seccode);
                    } else {
                        console.error('验证结果格式错误:', result);
                        showError('验证结果格式错误');
                    }
                });

                captchaObj.onError(function(error) {
                    console.error('验证码错误:', error);
                    showError('验证码加载失败，请刷新重试');
                });

                captchaObj.onClose(function() {
                    console.log('验证码弹窗关闭');
                });
            });
        }

        // 刷新验证码
        function refreshCaptcha() {
            console.log('刷新验证码');
            document.getElementById('captcha-box').innerHTML = '<div style="text-align: center; padding: 20px;">🔄 正在刷新验证码...</div>';

            // 重新初始化验证码
            initCaptcha();
        }

        function showError(message) {
            document.getElementById('captcha-box').innerHTML =
                '<div style="color: red; text-align: center; padding: 20px;">' +
                '❌ ' + message + '<br><button onclick="refreshCaptcha()" style="margin-top: 10px;">重新加载验证码</button></div>';
        }

        // 页面加载完成后初始化验证码
        document.addEventListener('DOMContentLoaded', function() {
            initCaptcha();
        });
        
        // 尝试自动绕过验证码
        function tryAutoBypass() {
            var btn = document.getElementById('autoBypassBtn');
            btn.disabled = true;
            btn.textContent = '🔄 正在尝试自动绕过...';

            // 使用固定的验证码结果尝试绕过
            var fakeValidate = 'auto_bypass_' + Date.now().toString(36);

            submitCaptchaResult(challenge, fakeValidate, fakeValidate + '|jordan')
            .finally(() => {
                btn.disabled = false;
                btn.textContent = '🚀 尝试自动绕过验证码';
            });
        }

        // 显示手动表单
        function showManualForm() {
            document.getElementById('captchaForm').style.display = 'block';
        }

        // 隐藏手动表单
        function hideManualForm() {
            document.getElementById('captchaForm').style.display = 'none';
        }

        // 提交验证码结果
        function submitCaptchaResult(challenge, validate, seccode) {
            var resultDiv = document.getElementById('result');

            return fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: 'geetest_challenge=' + encodeURIComponent(challenge) +
                      '&geetest_validate=' + encodeURIComponent(validate) +
                      '&geetest_seccode=' + encodeURIComponent(seccode)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = '✅ ' + data.message;
                    resultDiv.style.display = 'block';

                    // 跳转到支付页面
                    setTimeout(function() {
                        window.location.href = data.url;
                    }, 1000);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '❌ ' + data.message;
                    resultDiv.style.display = 'block';
                }
            })
            .catch(error => {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '❌ 请求失败: ' + error.message;
                resultDiv.style.display = 'block';
            });
        }

        // 跳转到官方验证页面
        function jumpToOfficial() {
            var officialUrl = 'https://pay.sdo.com/cashier/pay?orderToken=<?php echo urlencode($orderToken); ?>&orderId=<?php echo urlencode($orderId); ?>';

            if (confirm('将跳转到SDO官方验证页面完成验证，确认继续吗？')) {
                window.open(officialUrl, '_blank');

                // 显示提示信息
                var container = document.getElementById('captcha-box');
                container.innerHTML = '<div style="text-align: center; padding: 20px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; color: #155724;">' +
                    '<p><strong>🏢 已打开官方验证页面</strong></p>' +
                    '<p>请在新窗口中完成验证，验证成功后会自动跳转到支付页面。</p>' +
                    '<p style="font-size: 12px; margin-top: 10px;">如果新窗口没有打开，请检查浏览器弹窗拦截设置。</p>' +
                '</div>';
            }
        }

        // 处理表单提交
        document.getElementById('captchaForm').addEventListener('submit', function(e) {
            e.preventDefault();

            var challenge = document.getElementById('manual_challenge').value;
            var validate = document.getElementById('manual_validate').value;

            if (!validate) {
                alert('请输入验证码validate值');
                return;
            }

            var submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = true;
            submitBtn.textContent = '提交中...';

            submitCaptchaResult(challenge, validate, validate + '|jordan')
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.textContent = '提交验证码结果';
            });
        });

        // 页面加载完成后初始化
        window.onload = function() {
            console.log('页面加载完成，开始初始化验证码');

            // 等待极验库加载完成
            var checkGeetestLib = function() {
                if (typeof initGeetest !== 'undefined') {
                    console.log('极验库已加载，开始初始化验证码');
                    initCaptcha();
                } else {
                    console.log('等待极验库加载...');
                    setTimeout(checkGeetestLib, 100);
                }
            };

            checkGeetestLib();
        };
    </script>
</body>
</html>
