<?php
/**
 * SDO支付插件 - 自定义充值页面
 */

// 包含系统文件
include("./includes/common.php");

// 获取参数
$trade_no = $_GET['trade_no'] ?? '';
$sitename = $_GET['sitename'] ?? $conf['sitename'];

if (empty($trade_no)) {
    exit('订单号不能为空！');
}

// 获取订单信息
$order = $DB->getRow("SELECT * FROM pre_order WHERE trade_no=? LIMIT 1", [$trade_no]);
if (!$order) {
    exit('订单不存在！');
}

// 获取通道信息
$channel = $DB->getRow("SELECT * FROM pre_channel WHERE id=? LIMIT 1", [$order['channel']]);
if (!$channel) {
    exit('支付通道不存在！');
}

// 处理表单提交
if ($_POST['action'] == 'submit') {
    $amount = floatval($_POST['amount']);
    if ($amount < 1) {
        $error = '充值金额不能少于1元';
    } else {
        // 更新订单金额
        $DB->query("UPDATE pre_order SET money=? WHERE trade_no=?", [$amount, $trade_no]);

        // 重定向到支付页面
        $redirect_url = $siteurl . 'pay/' . $order['typename'] . '/' . $trade_no . '/?sitename=' . urlencode($sitename);
        header('Location: ' . $redirect_url);
        exit();
    }
}

// 获取游戏配置
$gameId = $channel['appid'] ?: 'GWPAY-791000810';
$gameServer = $channel['appsecret'] ?: '1-盟重';
$serverParts = explode('-', $gameServer);
$areaName = isset($serverParts[0]) ? $serverParts[0] . '区' : '1区';
$serverName = isset($serverParts[1]) ? $serverParts[1] : '盟重';

// 游戏名称映射
$gameNames = [
    'GWPAY-791000810' => '传奇新百区-盟重神兵',
    // 可以添加更多游戏
];
$gameName = isset($gameNames[$gameId]) ? $gameNames[$gameId] : '未知游戏';
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SDO游戏充值 - <?php echo $sitename; ?></title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 300;
        }
        .content {
            padding: 30px;
        }
        .game-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
            border-left: 4px solid #007bff;
        }
        .game-info h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 18px;
        }
        .game-info p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        .form-control:focus {
            outline: none;
            border-color: #007bff;
        }
        .amount-buttons {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }
        .amount-btn {
            padding: 12px;
            border: 2px solid #e9ecef;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s;
            font-size: 14px;
        }
        .amount-btn:hover {
            border-color: #007bff;
            background: #f8f9fa;
        }
        .amount-btn.active {
            border-color: #007bff;
            background: #007bff;
            color: white;
        }
        .submit-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .submit-btn:hover {
            transform: translateY(-2px);
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        .rate-info {
            background: #fff3cd;
            color: #856404;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #ffeaa7;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>SDO游戏充值</h1>
        </div>
        
        <div class="content">
            <?php if (isset($error)): ?>
                <div class="error"><?php echo htmlspecialchars($error); ?></div>
            <?php endif; ?>
            
            <div class="game-info">
                <h3><?php echo htmlspecialchars($gameName); ?></h3>
                <p><strong>游戏区服：</strong><?php echo htmlspecialchars($areaName . ' ' . $serverName); ?></p>
                <p><strong>订单号：</strong><?php echo htmlspecialchars($trade_no); ?></p>
            </div>
            
            <div class="rate-info">
                <strong>汇率说明：</strong>1元 ≈ 0.86个传奇币（实际汇率：1传奇币 = 1.16元）
            </div>
            
            <form method="post">
                <input type="hidden" name="action" value="submit">
                
                <div class="form-group">
                    <label>选择充值金额：</label>
                    <div class="amount-buttons">
                        <div class="amount-btn" data-amount="1.16">1个传奇币<br><small>1.16元</small></div>
                        <div class="amount-btn" data-amount="3.48">3个传奇币<br><small>3.48元</small></div>
                        <div class="amount-btn" data-amount="5.80">5个传奇币<br><small>5.80元</small></div>
                        <div class="amount-btn" data-amount="11.60">10个传奇币<br><small>11.60元</small></div>
                        <div class="amount-btn" data-amount="23.20">20个传奇币<br><small>23.20元</small></div>
                        <div class="amount-btn" data-amount="58.00">50个传奇币<br><small>58.00元</small></div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="amount">或输入自定义金额（元）：</label>
                    <input type="number" id="amount" name="amount" class="form-control" 
                           min="1.16" step="0.01" value="<?php echo $order['money']; ?>" 
                           placeholder="请输入充值金额" required>
                </div>
                
                <button type="submit" class="submit-btn">立即充值</button>
            </form>
        </div>
    </div>

    <script>
        // 金额按钮点击事件
        document.querySelectorAll('.amount-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // 移除其他按钮的active状态
                document.querySelectorAll('.amount-btn').forEach(b => b.classList.remove('active'));
                // 添加当前按钮的active状态
                this.classList.add('active');
                // 设置金额输入框的值
                document.getElementById('amount').value = this.dataset.amount;
            });
        });

        // 金额输入框变化时移除按钮的active状态
        document.getElementById('amount').addEventListener('input', function() {
            document.querySelectorAll('.amount-btn').forEach(b => b.classList.remove('active'));
        });
    </script>
</body>
</html>
